'use client'

import Link from 'next/link'
import React from 'react'
import { motion } from 'framer-motion'
import { Mail, Phone, MapPin } from 'lucide-react'

import type { Footer } from '@/payload-types'

import { CMSLink } from '@/components/Link'
import { Logo } from '@/components/Logo/Logo'

interface FooterClientProps {
  navItems: Footer['navItems']
}

export function FooterClient({ navItems: _navItems }: FooterClientProps) {
  // Main navigation items for quick links
  const mainNavItems = [
    { label: 'Home', href: '/' },
    { label: 'About', href: '/about' },
    { label: 'Projects', href: '/projects' },
    { label: 'Success Stories', href: '/success-stories' },
    { label: 'Partnerships', href: '/partnerships' },
    { label: 'Contact', href: '/contact' },
  ]

  return (
    <footer className="mt-0 bg-[#8A3E25] text-white relative overflow-hidden">
      {/* Main Footer Content - Reddish Brown Background Section */}
      <div className="relative z-10 bg-[#8A3E25] text-white">
        <div className="max-w-6xl mx-auto px-4 sm:px-6 lg:px-8 py-12">
          <div className="grid md:grid-cols-4 gap-8">
            {/* Logo and Description */}
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              whileInView={{ opacity: 1, y: 0 }}
              viewport={{ once: true }}
              transition={{ duration: 0.8 }}
              className="md:col-span-1"
            >
              <Link className="flex items-center mb-6 group" href="/">
                <Logo className="text-white group-hover:scale-105 transition-transform duration-300" />
              </Link>
              <p className="text-[#EFE3BA] mb-6 font-npi text-sm leading-[1.6] max-w-sm">
                Harnessing Indigenous Wealth for Sustainable Growth. Transforming Kenya&apos;s rich
                natural heritage into sustainable economic opportunities.
              </p>
              {/* Social Media Links - Skeleton Style with bigger icons */}
              <div className="flex gap-4">
                <motion.a
                  whileHover={{ scale: 1.2, rotate: 5 }}
                  whileTap={{ scale: 0.95 }}
                  href="https://facebook.com/npikenya"
                  target="_blank"
                  rel="noopener noreferrer"
                  className="group transition-all duration-300"
                  aria-label="Follow us on Facebook"
                >
                  <svg
                    className="w-10 h-10 text-[#EFE3BA]/80 hover:text-white transition-colors duration-300"
                    fill="none"
                    stroke="currentColor"
                    strokeWidth="1.5"
                    viewBox="0 0 24 24"
                  >
                    <path d="M18 2h-3a5 5 0 0 0-5 5v3H7v4h3v8h4v-8h3l1-4h-4V7a1 1 0 0 1 1-1h3z" />
                  </svg>
                </motion.a>
                <motion.a
                  whileHover={{ scale: 1.2, rotate: -5 }}
                  whileTap={{ scale: 0.95 }}
                  href="https://twitter.com/npikenya"
                  target="_blank"
                  rel="noopener noreferrer"
                  className="group transition-all duration-300"
                  aria-label="Follow us on Twitter"
                >
                  <svg
                    className="w-10 h-10 text-[#EFE3BA]/80 hover:text-white transition-colors duration-300"
                    fill="none"
                    stroke="currentColor"
                    strokeWidth="1.5"
                    viewBox="0 0 24 24"
                  >
                    <path d="M23 3a10.9 10.9 0 0 1-3.14 1.53 4.48 4.48 0 0 0-7.86 3v1A10.66 10.66 0 0 1 3 4s-4 9 5 13a11.64 11.64 0 0 1-7 2c9 5 20 0 20-11.5a4.5 4.5 0 0 0-.08-.83A7.72 7.72 0 0 0 23 3z" />
                  </svg>
                </motion.a>
                <motion.a
                  whileHover={{ scale: 1.2, rotate: 5 }}
                  whileTap={{ scale: 0.95 }}
                  href="https://linkedin.com/company/npikenya"
                  target="_blank"
                  rel="noopener noreferrer"
                  className="group transition-all duration-300"
                  aria-label="Connect with us on LinkedIn"
                >
                  <svg
                    className="w-10 h-10 text-[#EFE3BA]/80 hover:text-white transition-colors duration-300"
                    fill="none"
                    stroke="currentColor"
                    strokeWidth="1.5"
                    viewBox="0 0 24 24"
                  >
                    <path d="M16 8a6 6 0 0 1 6 6v7h-4v-7a2 2 0 0 0-2-2 2 2 0 0 0-2 2v7h-4v-7a6 6 0 0 1 6-6z" />
                    <rect x="2" y="9" width="4" height="12" />
                    <circle cx="4" cy="4" r="2" />
                  </svg>
                </motion.a>
                <motion.a
                  whileHover={{ scale: 1.2, rotate: -5 }}
                  whileTap={{ scale: 0.95 }}
                  href="https://instagram.com/npikenya"
                  target="_blank"
                  rel="noopener noreferrer"
                  className="group transition-all duration-300"
                  aria-label="Follow us on Instagram"
                >
                  <svg
                    className="w-10 h-10 text-[#EFE3BA]/80 hover:text-white transition-colors duration-300"
                    fill="none"
                    stroke="currentColor"
                    strokeWidth="1.5"
                    viewBox="0 0 24 24"
                  >
                    <rect x="2" y="2" width="20" height="20" rx="5" ry="5" />
                    <path d="M16 11.37A4 4 0 1 1 12.63 8 4 4 0 0 1 16 11.37z" />
                    <line x1="17.5" y1="6.5" x2="17.51" y2="6.5" />
                  </svg>
                </motion.a>
              </div>
            </motion.div>

            {/* Quick Links */}
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              whileInView={{ opacity: 1, y: 0 }}
              viewport={{ once: true }}
              transition={{ duration: 0.8, delay: 0.2 }}
              className="md:col-span-1"
            >
              <h3 className="text-white font-bold text-lg mb-6 font-npi">Quick Links</h3>
              <nav className="flex flex-col gap-3">
                {mainNavItems.map((item, i) => (
                  <motion.div
                    key={i}
                    whileHover={{ x: 5 }}
                    transition={{ type: 'spring', stiffness: 300 }}
                  >
                    <Link
                      href={item.href}
                      className="text-[#EFE3BA] hover:text-white transition-colors duration-300 text-sm font-medium"
                    >
                      {item.label}
                    </Link>
                  </motion.div>
                ))}
              </nav>
            </motion.div>

            {/* Resources */}
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              whileInView={{ opacity: 1, y: 0 }}
              viewport={{ once: true }}
              transition={{ duration: 0.8, delay: 0.3 }}
              className="md:col-span-1"
            >
              <h3 className="text-white font-bold text-lg mb-6 font-npi">Resources</h3>
              <ul className="space-y-3">
                {[
                  { label: 'IKIA Database', href: 'http://inkibank.museums.or.ke:8185/' },
                  { label: 'Publications', href: '/publications' },
                  { label: 'Training Materials', href: '/training' },
                  { label: 'Policy Documents', href: '/policy' },
                  { label: 'Downloads', href: '/downloads' },
                ].map((link, index) => (
                  <motion.li
                    key={index}
                    whileHover={{ x: 5 }}
                    transition={{ type: 'spring', stiffness: 300 }}
                  >
                    {link.href.startsWith('http') ? (
                      <a
                        href={link.href}
                        target="_blank"
                        rel="noopener noreferrer"
                        className="text-[#EFE3BA] hover:text-white transition-colors duration-300 font-medium text-sm"
                      >
                        {link.label}
                      </a>
                    ) : (
                      <Link
                        href={link.href}
                        className="text-[#EFE3BA] hover:text-white transition-colors duration-300 font-medium text-sm"
                      >
                        {link.label}
                      </Link>
                    )}
                  </motion.li>
                ))}
              </ul>
            </motion.div>

            {/* Contact Info */}
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              whileInView={{ opacity: 1, y: 0 }}
              viewport={{ once: true }}
              transition={{ duration: 0.8, delay: 0.4 }}
              className="md:col-span-1"
            >
              <h3 className="text-white font-bold text-lg mb-6 font-npi">Contact</h3>
              <div className="text-[#EFE3BA] space-y-4 font-npi">
                <motion.div
                  whileHover={{ x: 5 }}
                  className="flex items-start gap-3 group cursor-pointer"
                >
                  <MapPin className="w-6 h-6 text-[#25718A] group-hover:scale-110 transition-transform duration-300 flex-shrink-0 mt-0.5" />
                  <div className="min-w-0">
                    <p className="text-sm text-[#EFE3BA] leading-relaxed">
                      <strong>National Museums of Kenya,</strong> Museum Hill, Kipande Road,
                      Nairobi, Kenya
                    </p>
                  </div>
                </motion.div>
                <motion.div whileHover={{ x: 5 }} className="flex items-center gap-3 group">
                  <Mail className="w-6 h-6 text-[#25718A] group-hover:scale-110 transition-transform duration-300 flex-shrink-0" />
                  <a
                    href="mailto:<EMAIL>"
                    className="text-[#EFE3BA] hover:text-white transition-colors font-medium text-sm"
                  >
                    <EMAIL>
                  </a>
                </motion.div>
                <motion.div whileHover={{ x: 5 }} className="flex items-center gap-3 group">
                  <Phone className="w-6 h-6 text-[#25718A] group-hover:scale-110 transition-transform duration-300 flex-shrink-0" />
                  <a
                    href="tel:+254203742131"
                    className="text-[#EFE3BA] hover:text-white transition-colors font-medium text-sm"
                  >
                    +254 20 374 2131
                  </a>
                </motion.div>
              </div>
            </motion.div>
          </div>
        </div>
      </div>
    </footer>
  )
}
